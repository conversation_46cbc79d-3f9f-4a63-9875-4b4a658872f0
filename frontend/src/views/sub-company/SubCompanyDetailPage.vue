<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { toast } from 'vue-sonner'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  ArrowLeft,
  Loader2,
  Building2,
  Globe,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Edit,
} from 'lucide-vue-next'
import { useSubCompanyStore } from '@/stores'
import { storeToRefs } from 'pinia'
import Badge from '@/components/ui/badge/Badge.vue'

const subCompanyStore = useSubCompanyStore()
const {
  selectedSubCompany: subCompany,
  isLoadingDetails,
  error: storeError,
} = storeToRefs(subCompanyStore)
const { getSubCompanyById } = subCompanyStore

const route = useRoute()
const router = useRouter()
const companyId = route.params.company_id as string

async function fetchSubCompanyDetails() {
  if (isLoadingDetails.value) return
  if (!companyId) return router.push({ name: 'sub-companies' })

  const result = await getSubCompanyById(companyId)
  if (!result) {
    toast.error(storeError.value || 'Failed to fetch sub-company details')
    router.push({ name: 'sub-companies' })
  }
}

onMounted(async () => {
  await fetchSubCompanyDetails()
})
</script>

<template>
  <div class="w-full">
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center gap-3">
        <Button variant="ghost" size="sm" @click="router.back()">
          <ArrowLeft class="h-4 w-4" />
          <span>Back</span>
        </Button>
        <h1 class="text-2xl font-bold">
          {{ subCompany?.company_name ?? 'Sub-Company Detail' }}
        </h1>
      </div>
      <Button
        v-if="subCompany"
        size="sm"
        @click="router.push({ name: 'edit-sub-company', params: { company_id: companyId } })"
      >
        <Edit class="h-4 w-4 mr-2" />
        Edit
      </Button>
    </div>

    <div class="grid gap-6">
      <!-- Loading State -->
      <div v-if="isLoadingDetails" class="text-center py-8">
        <Loader2 class="animate-spin h-8 w-8 text-gray-500 mx-auto" />
      </div>

      <!-- Content -->
      <div v-else-if="subCompany" class="grid gap-6 md:grid-cols-2">
        <!-- Company Information -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <Building2 class="h-5 w-5" />
              Company Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <dl class="grid gap-4">
              <div>
                <dt class="text-sm font-medium text-gray-500">Company Name</dt>
                <dd class="text-base">{{ subCompany.company_name }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Company ID</dt>
                <dd class="text-base">{{ subCompany.company_id }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Beluga Sub-Company</dt>
                <dd class="text-base">{{ subCompany.beluga_sub_company }}</dd>
              </div>
              <div v-if="subCompany.company_website_url">
                <dt class="text-sm font-medium text-gray-500 flex items-center gap-1">
                  <Globe class="h-4 w-4" /> Website
                </dt>
                <dd class="text-base">
                  <a
                    :href="subCompany.company_website_url"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="text-blue-600 hover:underline"
                  >
                    {{ subCompany.company_website_url }}
                  </a>
                </dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Status</dt>
                <dd>
                  <Badge
                    :class="[
                      subCompany.status ? 'bg-green-200 text-green-800' : 'bg-red-200 text-red-800',
                    ]"
                  >
                    {{ subCompany.status ? 'Active' : 'Inactive' }}
                  </Badge>
                </dd>
              </div>
            </dl>
          </CardContent>
        </Card>

        <!-- Contact Information -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <Mail class="h-5 w-5" />
              Contact Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <dl class="grid gap-4">
              <div>
                <dt class="text-sm font-medium text-gray-500">Contact Person</dt>
                <dd class="text-base">{{ subCompany.first_name }} {{ subCompany.last_name }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500 flex items-center gap-1">
                  <Mail class="h-4 w-4" /> Email
                </dt>
                <dd class="text-base">
                  <a :href="'mailto:' + subCompany.email" class="text-blue-600 hover:underline">
                    {{ subCompany.email }}
                  </a>
                </dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500 flex items-center gap-1">
                  <Phone class="h-4 w-4" /> Phone
                </dt>
                <dd class="text-base">{{ subCompany.phone_number }}</dd>
              </div>
            </dl>
          </CardContent>
        </Card>

        <!-- Address -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <MapPin class="h-5 w-5" />
              Address
            </CardTitle>
          </CardHeader>
          <CardContent>
            <dl class="grid gap-4">
              <div>
                <dt class="text-sm font-medium text-gray-500">Street Address</dt>
                <dd class="text-base">
                  {{ subCompany.address_line_1 }}
                  <span v-if="subCompany.address_line_2"
                    ><br />{{ subCompany.address_line_2 }}</span
                  >
                </dd>
              </div>
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <dt class="text-sm font-medium text-gray-500">City</dt>
                  <dd class="text-base">{{ subCompany.city }}</dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500">State</dt>
                  <dd class="text-base">{{ subCompany.state }}</dd>
                </div>
              </div>
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <dt class="text-sm font-medium text-gray-500">Country</dt>
                  <dd class="text-base">{{ subCompany.country }}</dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500">ZIP Code</dt>
                  <dd class="text-base">{{ subCompany.zipcode }}</dd>
                </div>
              </div>
            </dl>
          </CardContent>
        </Card>

        <!-- API Information -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <Calendar class="h-5 w-5" />
              Visit Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <dl class="grid gap-4">
              <div>
                <dt class="text-sm font-medium text-gray-500">Visit Prefix</dt>
                <dd class="text-base break-all">
                  {{ subCompany.visit_prefix }}
                </dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Webhook URL</dt>
                <dd class="text-base break-all">{{ subCompany.visit_webhook_url }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Created</dt>
                <dd class="text-base">{{ subCompany.created_date_time }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                <dd class="text-base">
                  {{ subCompany.updated_date_time }}
                  <span class="text-sm text-gray-500">
                    by {{ subCompany.action_by_user_name }}
                  </span>
                </dd>
              </div>
            </dl>
          </CardContent>
        </Card>
      </div>

      <!-- Error State -->
      <Card v-else-if="storeError">
        <CardContent class="text-center py-8">
          <p class="text-red-500">{{ storeError }}</p>
          <Button variant="secondary" size="sm" class="mt-4" @click="fetchSubCompanyDetails">
            Retry
          </Button>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
