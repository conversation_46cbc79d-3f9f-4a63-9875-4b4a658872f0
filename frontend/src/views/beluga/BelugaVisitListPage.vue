<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import dayjs from 'dayjs'
import { CalendarIcon, EyeIcon, Search, XIcon } from 'lucide-vue-next'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { useBelugaVisitStore } from '@/stores'
import type { AcceptableValue } from 'reka-ui'
import { cn, formatPhone } from '@/lib'
import { RangeCalendar } from '@/components/ui/range-calendar'
import { useDebounce } from '@vueuse/core'
import { toast } from 'vue-sonner'
import router from '@/router'

const belugaVisitStore = useBelugaVisitStore()
const {
  belugaVisits,
  isLoadingList,
  pagination,
  error: storeError,
  searchQuery,
  filterSubCompanyId,
  dateRange,
  sortBy,
  subCompanyList,
} = storeToRefs(belugaVisitStore)
const { fetchBelugaVisits, getSubCompanyList } = belugaVisitStore

const debouncedSearchQuery = useDebounce(searchQuery, 300)

const handleSearch = async () => {
  pagination.value.currentPage = 1
  await fetchBelugaVisits()
}

const handleSort = async (column: string) => {
  if (sortBy.value.column === column) {
    sortBy.value.direction = sortBy.value.direction === 'asc' ? 'desc' : 'asc'
  } else {
    sortBy.value.column = column
    sortBy.value.direction = 'desc'
  }
  await fetchBelugaVisits()
}

const handlePageChange = async (page: number) => {
  pagination.value.currentPage = page
  await fetchBelugaVisits()
}

const handlePerPageChange = async (value: AcceptableValue) => {
  pagination.value.perPage = Number(value)
  pagination.value.currentPage = 1
  await fetchBelugaVisits()
}

async function resetFilters() {
  searchQuery.value = ''
  filterSubCompanyId.value = 'all'
  dateRange.value = { start: undefined, end: undefined }
  sortBy.value = { column: '', direction: 'desc' }
  pagination.value.currentPage = 1
  pagination.value.perPage = 10
  await fetchBelugaVisits()
}

function resolveStatusColor(status: number) {
  switch (status) {
    case 0:
    case 2:
    case 3:
    case 5:
    case 6:
      return 'bg-red-500/50'
    case 1:
      return 'bg-blue-500/50'
    case 4:
      return 'bg-green-500/50'
    default:
      return 'bg-gray-500/50'
  }
}

watch(
  () => debouncedSearchQuery.value,
  () => {
    handleSearch()
  },
)

onMounted(async () => {
  await Promise.all([fetchBelugaVisits(), getSubCompanyList()])
  if (storeError.value) {
    toast.error(storeError.value)
  }
})
</script>

<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">Beluga Visits</h1>
        <p class="text-muted-foreground text-sm">Manage Beluga visits created by Sub Companies</p>
      </div>
    </div>

    <Card>
      <CardHeader class="pb-3">
        <div
          class="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-x-4 sm:space-y-0"
        >
          <div class="relative w-full sm:max-w-sm">
            <Search class="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              v-model="searchQuery"
              placeholder="Search visits..."
              class="pl-8"
              @keyup.enter="handleSearch"
            />
            <XIcon
              v-if="searchQuery"
              class="absolute right-2.5 top-2.5 h-4 w-4 cursor-pointer text-muted-foreground"
              @click="searchQuery = ''"
            />
          </div>
          <div class="flex items-center space-x-2">
            <Select v-model="filterSubCompanyId" @update:model-value="handleSearch">
              <SelectTrigger class="w-[180px]">
                <SelectValue placeholder="Sub Company" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Sub Companies</SelectItem>
                <SelectItem
                  v-for="subCompany in subCompanyList"
                  :key="subCompany.id"
                  :value="subCompany.id"
                  >{{ subCompany.company_name }}</SelectItem
                >
              </SelectContent>
            </Select>

            <Popover>
              <PopoverTrigger as-child>
                <Button
                  id="date"
                  variant="outline"
                  :class="
                    cn(
                      'min-w-[220px] justify-start text-left font-normal',
                      !dateRange && 'text-muted-foreground',
                    )
                  "
                >
                  <CalendarIcon class="mr-2 h-4 w-4" />
                  <template v-if="dateRange.start">
                    <template v-if="dateRange.end">
                      {{ dayjs(dateRange.start.toString()).format('MMM DD, YYYY') }} -
                      {{ dayjs(dateRange.end.toString()).format('MMM DD, YYYY') }}
                    </template>
                    <template v-else>
                      {{ dayjs(dateRange.start.toString()).format('MMM DD, YYYY') }}
                    </template>
                  </template>
                  <template v-else> Filter by date range </template>
                </Button>
              </PopoverTrigger>
              <PopoverContent class="w-auto p-0" align="end">
                <RangeCalendar
                  v-model="dateRange"
                  initial-focus
                  :number-of-months="1"
                  @update:start-value="(startDate) => (dateRange.start = startDate)"
                  @update:model-value="handleSearch"
                />
              </PopoverContent>
            </Popover>

            <Button variant="outline" @click="resetFilters"> Reset </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div class="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>#</TableHead>
                <TableHead>Beluga Master ID</TableHead>
                <TableHead>Beluga Visit Type</TableHead>
                <TableHead>Sub Company Master ID</TableHead>
                <TableHead>Sub Company</TableHead>
                <TableHead>Status</TableHead>
                <TableHead
                  class="cursor-pointer hover:bg-muted/50"
                  @click="handleSort('created_at')"
                >
                  <div class="flex items-center">
                    Created At
                    <span v-if="sortBy.column === 'created_at'" class="ml-1">
                      {{ sortBy.direction === 'asc' ? '↑' : '↓' }}
                    </span>
                  </div>
                </TableHead>
                <TableHead
                  class="cursor-pointer hover:bg-muted/50"
                  @click="handleSort('updated_at')"
                >
                  <div class="flex items-center">
                    Updated At
                    <span v-if="sortBy.column === 'updated_at'" class="ml-1">
                      {{ sortBy.direction === 'asc' ? '↑' : '↓' }}
                    </span>
                  </div>
                </TableHead>
                <TableHead class="w-[100px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <!-- Loading -->
              <template v-if="isLoadingList">
                <TableRow>
                  <TableCell colspan="9" class="h-24 text-center"> Loading... </TableCell>
                </TableRow>
              </template>

              <!-- No results -->
              <template v-else-if="belugaVisits.length === 0">
                <TableRow>
                  <TableCell colspan="9" class="h-24 text-center"> No results found. </TableCell>
                </TableRow>
              </template>

              <!-- Data -->
              <template v-else>
                <TableRow v-for="(item, index) in belugaVisits" :key="item.id">
                  <TableCell class="font-medium">
                    {{ index + 1 + (pagination.currentPage - 1) * pagination.perPage }}
                  </TableCell>
                  <TableCell class="font-medium">
                    {{ item.beluga_master_id }}
                  </TableCell>
                  <TableCell>{{ item.beluga_visit_type }}</TableCell>
                  <TableCell>{{ item.sub_company_master_id }}</TableCell>
                  <TableCell>
                    {{ item.sub_company.company_name }}
                  </TableCell>
                  <TableCell>
                    <div
                      class="inline-block rounded-full text-xs px-3 py-0.5"
                      :class="resolveStatusColor(item.status)"
                    >
                      {{ item.visit_status_text }}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>{{ item.created_date }}</div>
                    <div class="text-xs text-muted-foreground">
                      {{ item.created_time }}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>{{ item.updated_date }}</div>
                    <div class="text-xs text-muted-foreground">
                      {{ item.updated_time }}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div class="flex space-x-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        @click="
                          router.push({
                            name: 'beluga-visit-detail',
                            params: { visit_id: item.id },
                          })
                        "
                      >
                        <EyeIcon class="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              </template>
            </TableBody>
          </Table>
        </div>

        <!-- Pagination -->
        <div class="flex items-center justify-between px-2 mt-4">
          <div class="flex items-center space-x-2">
            <p class="text-sm text-muted-foreground">
              Page {{ pagination.currentPage }} of {{ pagination.totalPages }}
            </p>
            <Select :model-value="pagination.perPage" @update:model-value="handlePerPageChange">
              <SelectTrigger class="h-8">
                <SelectValue :placeholder="pagination.perPage.toString()" />
              </SelectTrigger>
              <SelectContent side="top">
                <SelectItem
                  v-for="pageSize in [10, 25, 50, 100]"
                  :key="pageSize"
                  :value="pageSize"
                  class="cursor-pointer"
                >
                  {{ pageSize }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div class="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              :disabled="pagination.currentPage === 1"
              @click="handlePageChange(pagination.currentPage - 1)"
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              :disabled="pagination.currentPage >= pagination.totalPages"
              @click="handlePageChange(pagination.currentPage + 1)"
            >
              Next
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
