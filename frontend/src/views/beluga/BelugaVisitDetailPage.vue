<script lang="ts" setup>
import { useRoute } from 'vue-router'
import { useBelugaVisitStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { onMounted, ref } from 'vue'
import { Loader2Icon, ArrowLeft, EyeIcon } from 'lucide-vue-next'
import router from '@/router'
import { Button } from '@/components/ui/button'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from '@/components/ui/table'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import Badge from '@/components/ui/badge/Badge.vue'
import { toast } from 'vue-sonner'

const belugaVisitStore = useBelugaVisitStore()
const {
  belugaVisitDetail,
  isLoadingVisitDetail,
  visitLogDetail,
  isLoadingVisitLogDetail,
  error: storeError,
} = storeToRefs(belugaVisitStore)
const { fetchBelugaVisitDetail, fetchVisitLogDetail } = belugaVisitStore

const route = useRoute()
const visitId = route.params.visit_id
const isVisitLogDetailOpen = ref(false)

function resolveStatusColor(status: number) {
  switch (status) {
    case 0:
    case 2:
    case 3:
    case 5:
    case 6:
      return 'bg-red-500/50'
    case 1:
      return 'bg-blue-500/50'
    case 4:
      return 'bg-green-500/50'
    default:
      return 'bg-gray-500/50'
  }
}

async function openVisitLogDetail(logId: string) {
  isVisitLogDetailOpen.value = true
  await fetchVisitLogDetail(logId)
  if (storeError.value) {
    toast.error(storeError.value)
    isVisitLogDetailOpen.value = false
  }
}

onMounted(async () => {
  if (visitId) {
    await fetchBelugaVisitDetail(visitId as string)
    if (storeError.value) {
      toast.error(storeError.value)
      router.back()
    }
  }
})
</script>

<template>
  <div class="min-h-screen">
    <div class="flex items-center gap-3">
      <div class="mb-4">
        <Button variant="ghost" size="sm" @click="router.back()">
          <ArrowLeft class="h-4 w-4" />
          <span>Back</span>
        </Button>
      </div>
      <h1 class="text-2xl font-bold mb-4">Beluga Visit Detail</h1>
    </div>

    <!-- Loading State -->
    <div v-if="isLoadingVisitDetail" class="flex flex-col justify-center items-center gap-3 h-64">
      <Loader2Icon class="animate-spin h-8 w-8" />
      <span class="text-gray-500">Loading...</span>
    </div>

    <!-- Actual Content -->
    <div v-else-if="belugaVisitDetail" class="space-y-6">
      <!-- Responsive Card Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Visit Information -->
        <Card>
          <CardHeader>
            <CardTitle>Visit Information</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableBody>
                <TableRow>
                  <TableCell><Label>Beluga Master ID</Label></TableCell>
                  <TableCell>{{ belugaVisitDetail.beluga_master_id ?? '—' }}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell><Label>Sub-company Master ID</Label></TableCell>
                  <TableCell>{{ belugaVisitDetail.sub_company_master_id ?? '—' }}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell><Label>Visit Type</Label></TableCell>
                  <TableCell>{{ belugaVisitDetail.beluga_visit_type ?? '—' }}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell><Label>Visit Outcome</Label></TableCell>
                  <TableCell>{{ belugaVisitDetail.beluga_visit_outcome ?? '—' }}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell><Label>Status</Label></TableCell>
                  <TableCell>
                    <div
                      class="inline-block rounded-full text-xs px-3 py-0.5"
                      :class="resolveStatusColor(belugaVisitDetail.status)"
                    >
                      {{ belugaVisitDetail.visit_status_text ?? '—' }}
                    </div>
                  </TableCell>
                </TableRow>
                <TableRow v-if="belugaVisitDetail.sub_company.company_name">
                  <TableCell><Label>Company</Label></TableCell>
                  <TableCell>{{ belugaVisitDetail.sub_company.company_name ?? '—' }}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell><Label>Created At</Label></TableCell>
                  <TableCell>{{ belugaVisitDetail.created_date_time ?? '—' }}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell><Label>Updated At</Label></TableCell>
                  <TableCell>{{ belugaVisitDetail.updated_date_time ?? '—' }}</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        <!-- Medicine Sent to Beluga -->
        <Card v-if="belugaVisitDetail.medicine_data_sent_beluga">
          <CardHeader>
            <CardTitle>Medicine Sent to Beluga</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableBody>
                <TableRow>
                  <TableCell><Label>Name</Label></TableCell>
                  <TableCell>{{
                    belugaVisitDetail.medicine_data_sent_beluga.name ?? '—'
                  }}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell><Label>Strength</Label></TableCell>
                  <TableCell>{{
                    belugaVisitDetail.medicine_data_sent_beluga.strength ?? '—'
                  }}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell><Label>Quantity</Label></TableCell>
                  <TableCell>{{
                    belugaVisitDetail.medicine_data_sent_beluga.quantity ?? '—'
                  }}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell><Label>Med ID</Label></TableCell>
                  <TableCell>{{
                    belugaVisitDetail.medicine_data_sent_beluga.medId ?? '—'
                  }}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell><Label>Refills</Label></TableCell>
                  <TableCell>{{
                    belugaVisitDetail.medicine_data_sent_beluga.refills ?? '—'
                  }}</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        <!-- Medicine Received from Beluga -->
        <Card v-if="belugaVisitDetail.medicine_data_received_from_beluga" class="lg:col-span-2">
          <!-- Provider Details -->
          <template v-if="belugaVisitDetail.provider_details">
            <CardHeader>
              <CardTitle>Provider Details</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableBody>
                  <TableRow>
                    <TableCell><Label>NPI</Label></TableCell>
                    <TableCell>{{ belugaVisitDetail.provider_details.docNpi ?? '—' }}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell><Label>Name</Label></TableCell>
                    <TableCell>
                      {{ belugaVisitDetail.provider_details.docFirstName ?? '' }}
                      {{ belugaVisitDetail.provider_details.docLastName ?? '' }}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </template>
          <CardHeader>
            <CardTitle>Medicine Received from Beluga</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableBody>
                <TableRow>
                  <TableCell><Label>Name</Label></TableCell>
                  <TableCell>{{
                    belugaVisitDetail.medicine_data_received_from_beluga.name ?? '—'
                  }}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell><Label>Strength</Label></TableCell>
                  <TableCell>{{
                    belugaVisitDetail.medicine_data_received_from_beluga.strength ?? '—'
                  }}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell><Label>Quantity</Label></TableCell>
                  <TableCell>{{
                    belugaVisitDetail.medicine_data_received_from_beluga.quantity ?? '—'
                  }}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell><Label>Refills</Label></TableCell>
                  <TableCell>{{
                    belugaVisitDetail.medicine_data_received_from_beluga.refills ?? '—'
                  }}</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      <!-- Visit Logs (full width) -->
      <Card v-if="belugaVisitDetail.visit_logs && belugaVisitDetail.visit_logs.length > 0">
        <CardHeader>
          <CardTitle>Visit Logs</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Event</TableHead>
                <TableHead>From</TableHead>
                <TableHead>To</TableHead>
                <TableHead>Status Code</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-for="log in belugaVisitDetail.visit_logs" :key="log.id">
                <TableCell>
                  <Badge
                    v-if="log.event_type && log.event_status_code"
                    :variant="log.event_status_code === 200 ? 'secondary' : 'destructive'"
                  >
                    {{ log.event_type }}
                  </Badge>
                  <Badge v-else-if="log.event_type && !log.event_status_code" variant="secondary">
                    {{ log.event_type }}
                  </Badge>
                  <Badge v-else> — </Badge>
                </TableCell>
                <TableCell>{{ log.request_from ?? '—' }}</TableCell>
                <TableCell>{{ log.request_to ?? '—' }}</TableCell>
                <TableCell>
                  <Badge
                    v-if="log.event_status_code && log.event_status_code === 200"
                    variant="default"
                  >
                    {{ log.event_status_code }}
                  </Badge>
                  <Badge
                    v-else-if="log.event_status_code && log.event_status_code !== 200"
                    variant="destructive"
                  >
                    {{ log.event_status_code }}
                  </Badge>
                  <span v-else> — </span>
                </TableCell>
                <TableCell>{{ log.created_date_time ?? '—' }}</TableCell>
                <TableCell>
                  <Button
                    v-if="log.event_status_code"
                    variant="link"
                    size="icon"
                    @click="openVisitLogDetail(log.id)"
                  >
                    <EyeIcon class="h-4 w-4" />
                  </Button>
                  <span v-else> - </span>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>

    <!-- Visit Log Detail -->
    <Dialog v-model:open="isVisitLogDetailOpen">
      <DialogContent class="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>Visit Log Detail</DialogTitle>
        </DialogHeader>
        <div v-if="isLoadingVisitLogDetail" class="grid place-items-center">
          <Loader2Icon class="animate-spin h-8 w-8" />
          <span class="text-gray-500">Loading...</span>
        </div>
        <div v-else class="flex items-center space-x-2">
          <pre
            v-if="visitLogDetail"
            class="w-full max-h-96 overflow-auto bg-secondary p-4 rounded"
            >{{ visitLogDetail }}</pre
          >
          <div v-else class="text-center py-3">
            <span class="text-disabled">No Data Available</span>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>
