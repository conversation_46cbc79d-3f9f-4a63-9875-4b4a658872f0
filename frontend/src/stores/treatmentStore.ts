import { defineStore } from 'pinia'
import { computed, ref, type Ref } from 'vue'
import { apiClient } from '@/composables/useApi'
import { toast } from 'vue-sonner'
import type { ApiResponse } from '@/types/api'
import { processErrors } from '@/lib'
import type { DateRange } from 'reka-ui'

// types
export type Treatment = {
  id: string
  treatment_name: string
  beluga_visit_type: string
  is_active: 0 | 1
  created_date: string
  created_time: string
  updated_date: string
  updated_time: string

  // custom field
  status: boolean
}

export type TreatmentDetail = Treatment & {
  created_date_time: string
  updated_date_time: string
  action_by_user_name: string
  action_at: string
}

export type TreatmentsListPayload = {
  searchQuery?: string
  is_active?: '' | 0 | 1 // Empty string for all, 0 for inactive, 1 for active
  from_date?: string
  to_date?: string
  page?: number
  perPage?: number
  sortByColumnName?: string
  isSortDirDesc?: 'asc' | 'desc'
}

export type TreatmentsListResponse = {
  current_page: number
  per_page: number
  totalPage: number
  totalRecords: number
  records: Treatment[]
}

export type TreatmentPayload = {
  treatment_name: string
  beluga_visit_type: string
}

export type TreatmentOption = Pick<Treatment, 'id' | 'treatment_name'>

// store definition
export const useTreatmentStore = defineStore('treatmentStore', () => {
  // state
  const treatments = ref<Treatment[]>([])
  const selectedTreatment = ref<TreatmentDetail | null>(null)
  const isLoadingList = ref(false)
  const isLoadingDetails = ref(false)
  const isAdding = ref(false)
  const isUpdating = ref(false)
  const isDeleting = ref(false)
  const isTogglingStatus = ref(false)
  const error = ref<string | null>(null)
  const fieldErrors = ref<Record<string, string[]>>({})
  const pagination = ref({
    currentPage: 1,
    perPage: 10,
    totalPages: 1,
    totalRecords: 0,
  })
  const searchQuery = ref('')
  const statusFilter = ref<string>('all')
  const dateRange = ref({
    start: undefined,
    end: undefined,
  }) as Ref<DateRange>
  const sortBy = ref<{
    column: string
    direction: 'asc' | 'desc'
  }>({
    column: '',
    direction: 'desc',
  })

  const treatmentOptions = ref<TreatmentOption[]>([])
  const isLoadingTreatmentOptions = ref(false)

  // getters
  const listPayload = computed<Partial<TreatmentsListPayload>>(() => ({
    searchQuery: searchQuery.value || undefined,
    page: pagination.value.currentPage,
    perPage: pagination.value.perPage,
    sortByColumnName: sortBy.value.column || undefined,
    isSortDirDesc: sortBy.value.direction,
    is_active: statusFilter.value !== 'all' ? (Number(statusFilter.value) as unknown as 0 | 1) : '',
    from_date: dateRange.value.start
      ? `${dateRange.value.start.month}/${dateRange.value.start.day}/${dateRange.value.start.year}`
      : undefined,
    to_date: dateRange.value.end
      ? `${dateRange.value.end.month}/${dateRange.value.end.day}/${dateRange.value.end.year}`
      : undefined,
  }))

  // actions
  async function fetchTreatments() {
    isLoadingList.value = true
    error.value = null

    try {
      const response = await apiClient.post<
        ApiResponse & { treatmentData: TreatmentsListResponse }
      >('/admin/list-treatment', listPayload.value)

      if (response.data.status === 200) {
        treatments.value = response.data.treatmentData.records.map((treatment) => ({
          ...treatment,
          status: Boolean(treatment.is_active),
        }))

        pagination.value = {
          currentPage: response.data.treatmentData.current_page,
          perPage: response.data.treatmentData.per_page,
          totalPages: response.data.treatmentData.totalPage,
          totalRecords: response.data.treatmentData.totalRecords,
        }

        return true
      } else {
        error.value = response.data.message || 'Failed to fetch treatments'
        return false
      }
    } catch (err: any) {
      console.error('Error fetching treatments:', err)
      error.value = processErrors(err, 'An error occurred while fetching treatments.')
      return false
    } finally {
      isLoadingList.value = false
    }
  }

  async function getTreatmentById(id: string) {
    isLoadingDetails.value = true
    error.value = null

    try {
      const response = await apiClient.get<ApiResponse & { treatmentDetails: TreatmentDetail }>(
        `/admin/treatment/${id}`,
      )

      if (response.data.status === 200) {
        selectedTreatment.value = response.data.treatmentDetails
        return true
      } else {
        error.value = response.data.message || `Failed to fetch treatment with ID: ${id}.`
        return false
      }
    } catch (err: any) {
      console.error('Error fetching treatment details:', err)
      error.value = processErrors(err, 'An error occurred while fetching treatment details.')
      return false
    } finally {
      isLoadingDetails.value = false
    }
  }

  async function addTreatment(payload: TreatmentPayload) {
    isAdding.value = true
    error.value = null
    fieldErrors.value = {}

    try {
      const response = await apiClient.post<ApiResponse>('/admin/add-treatment', payload)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Treatment added successfully')
        return true
      } else if (response.data.errors) {
        fieldErrors.value = response.data.errors
        return false
      } else {
        error.value = response.data.message || 'Failed to add treatment'
        return false
      }
    } catch (err: any) {
      console.error('Error adding treatment:', err)
      error.value = processErrors(err, 'An error occurred while adding treatment.')
      return false
    } finally {
      isAdding.value = false
    }
  }

  async function updateTreatment(payload: TreatmentPayload & { id: string }) {
    isUpdating.value = true
    error.value = null
    fieldErrors.value = {}

    try {
      const response = await apiClient.post<ApiResponse>('/admin/update-treatment', payload)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Treatment updated successfully')
        return true
      } else if (response.data.errors) {
        fieldErrors.value = response.data.errors
        return false
      } else {
        error.value = response.data.message || 'Failed to update treatment'
        return false
      }
    } catch (err: any) {
      console.error('Error updating treatment:', err)
      error.value = processErrors(err, 'An error occurred while updating treatment.')
      return false
    } finally {
      isUpdating.value = false
    }
  }

  async function toggleTreatmentStatus(id: string) {
    isTogglingStatus.value = true
    error.value = null

    try {
      const response = await apiClient.get<ApiResponse>(`/admin/update-treatment-status/${id}`)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Treatment status updated successfully')
        return true
      } else {
        error.value = response.data.message || 'Failed to update treatment status'
        return false
      }
    } catch (err: any) {
      console.error('Error updating treatment status:', err)
      error.value = processErrors(err, 'An error occurred while updating treatment status.')
      return false
    } finally {
      isTogglingStatus.value = false
    }
  }

  async function deleteTreatment(id: string) {
    isDeleting.value = true
    error.value = null

    try {
      const response = await apiClient.delete<ApiResponse>(`/admin/delete-treatment/${id}`)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Treatment deleted successfully')
        return true
      } else {
        error.value = response.data.message || 'Failed to delete treatment'
        return false
      }
    } catch (err: any) {
      console.error('Error deleting treatment:', err)
      error.value = processErrors(err, 'An error occurred while deleting treatment.')
      return false
    } finally {
      isDeleting.value = false
    }
  }

  const fetchTreatmentOptions = async () => {
    isLoadingTreatmentOptions.value = true
    error.value = null

    try {
      const response = await apiClient.get<ApiResponse & { treatments: TreatmentOption[] }>(
        '/admin/all-treatments',
      )

      if (response.data.status === 200 && Array.isArray(response.data.treatments)) {
        treatmentOptions.value = response.data.treatments
        return true
      } else {
        error.value = response.data.message || 'Failed to load treatments'
        return false
      }
    } catch (err) {
      console.error('Error fetching treatments:', err)
      error.value = processErrors(err, 'An error occurred while fetching treatments')
      return false
    } finally {
      isLoadingTreatmentOptions.value = false
    }
  }

  return {
    // State
    treatments,
    selectedTreatment,
    pagination,
    isLoadingList,
    isLoadingDetails,
    isAdding,
    isUpdating,
    isTogglingStatus,
    isDeleting,
    error,
    fieldErrors,
    searchQuery,
    statusFilter,
    dateRange,
    sortBy,
    treatmentOptions,
    isLoadingTreatmentOptions,

    // Actions
    fetchTreatments,
    getTreatmentById,
    addTreatment,
    updateTreatment,
    toggleTreatmentStatus,
    deleteTreatment,
    fetchTreatmentOptions,
  }
})
