import { defineStore } from 'pinia'
import { computed, ref, type Ref } from 'vue'
import { apiClient } from '@/composables/useApi'
import { toast } from 'vue-sonner'
import type { ApiResponse } from '@/types/api'
import { processErrors } from '@/lib'
import type { DateRange } from 'reka-ui'

// types
export type Pharmacy = {
  id: string
  pharmacy_name: string
  pharmacy_type: string
  pharmacy_id: string
  is_active: 0 | 1
  created_date: string
  created_time: string
  updated_date: string
  updated_time: string

  // custom field
  status: boolean
}

export type PharmacyDetail = Pharmacy & {
  created_date_time: string
  updated_date_time: string
}

export type PharmacyListPayload = {
  searchQuery?: string
  is_active?: '' | 0 | 1 // Empty string for all, 0 for inactive, 1 for active
  from_date?: string
  to_date?: string
  page?: number
  perPage?: number
  sortByColumnName?: string
  isSortDirDesc?: 'asc' | 'desc'
}

export type PharmacyListResponse = {
  current_page: number
  per_page: number
  totalPage: number
  totalRecords: number
  records: Pharmacy[]
}

export type PharmacyPayload = {
  pharmacy_name: string
  pharmacy_type: string
  pharmacy_id: string
}

// store definition
export const usePharmacyStore = defineStore('pharmacyStore', () => {
  // state
  const pharmacies = ref<Pharmacy[]>([])
  const selectedPharmacy = ref<PharmacyDetail | null>(null)
  const isLoadingList = ref(false)
  const isLoadingDetails = ref(false)
  const isAdding = ref(false)
  const isUpdating = ref(false)
  const isDeleting = ref(false)
  const isTogglingStatus = ref(false)
  const error = ref<string | null>(null)
  const fieldErrors = ref<Record<string, string[]>>({})
  const pagination = ref({
    currentPage: 1,
    perPage: 10,
    totalPages: 1,
    totalRecords: 0,
  })
  const searchQuery = ref('')
  const statusFilter = ref<string>('all')
  const dateRange = ref({
    start: undefined,
    end: undefined,
  }) as Ref<DateRange>
  const sortBy = ref<{
    column: string
    direction: 'asc' | 'desc'
  }>({
    column: '',
    direction: 'desc',
  })

  // getters
  const listPayload = computed<Partial<PharmacyListPayload>>(() => ({
    searchQuery: searchQuery.value || undefined,
    page: pagination.value.currentPage,
    perPage: pagination.value.perPage,
    sortByColumnName: sortBy.value.column || undefined,
    isSortDirDesc: sortBy.value.direction,
    is_active: statusFilter.value !== 'all' ? (Number(statusFilter.value) as unknown as 0 | 1) : '',
    from_date: dateRange.value.start
      ? `${dateRange.value.start.month}/${dateRange.value.start.day}/${dateRange.value.start.year}`
      : undefined,
    to_date: dateRange.value.end
      ? `${dateRange.value.end.month}/${dateRange.value.end.day}/${dateRange.value.end.year}`
      : undefined,
  }))

  // actions
  async function fetchPharmacies() {
    isLoadingList.value = true
    error.value = null

    try {
      const response = await apiClient.post<ApiResponse & { pharmacyData: PharmacyListResponse }>(
        '/admin/list-pharmacy',
        listPayload.value,
      )

      if (response.data.status === 200) {
        pharmacies.value = response.data.pharmacyData.records.map((pharmacy) => ({
          ...pharmacy,
          status: Boolean(pharmacy.is_active),
        }))

        pagination.value = {
          currentPage: response.data.pharmacyData.current_page,
          perPage: response.data.pharmacyData.per_page,
          totalPages: response.data.pharmacyData.totalPage,
          totalRecords: response.data.pharmacyData.totalRecords,
        }

        return true
      } else {
        error.value = response.data.message || 'Failed to fetch pharmacies'
        return false
      }
    } catch (err: any) {
      console.error('Error fetching pharmacies:', err)
      error.value = processErrors(err, 'An error occurred while fetching pharmacies.')
      return false
    } finally {
      isLoadingList.value = false
    }
  }

  async function getPharmacyById(id: string) {
    isLoadingDetails.value = true
    error.value = null

    try {
      const response = await apiClient.get<ApiResponse & { pharmacyDetails: PharmacyDetail }>(
        `/admin/pharmacy/${id}`,
      )

      if (response.data.status === 200) {
        selectedPharmacy.value = response.data.pharmacyDetails
        return true
      } else {
        error.value = response.data.message || `Failed to fetch pharmacy with ID: ${id}.`
        return false
      }
    } catch (err: any) {
      console.error('Error fetching pharmacy details:', err)
      error.value = processErrors(err, 'An error occurred while fetching pharmacy details.')
      return false
    } finally {
      isLoadingDetails.value = false
    }
  }

  async function addPharmacy(payload: PharmacyPayload) {
    isAdding.value = true
    error.value = null
    fieldErrors.value = {}

    try {
      const response = await apiClient.post<ApiResponse>('/admin/add-pharmacy', payload)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Pharmacy added successfully')
        return true
      } else if (response.data.errors) {
        fieldErrors.value = response.data.errors
        return false
      } else {
        error.value = response.data.message || 'Failed to add pharmacy'
        return false
      }
    } catch (err: any) {
      console.error('Error adding pharmacy:', err)
      error.value = processErrors(err, 'An error occurred while adding pharmacy.')
      return false
    } finally {
      isAdding.value = false
    }
  }

  async function updatePharmacy(payload: PharmacyPayload & { id: string }) {
    isUpdating.value = true
    error.value = null
    fieldErrors.value = {}

    try {
      const response = await apiClient.post<ApiResponse>('/admin/update-pharmacy', payload)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Pharmacy updated successfully')
        return true
      } else if (response.data.errors) {
        fieldErrors.value = response.data.errors
        return false
      } else {
        error.value = response.data.message || 'Failed to update pharmacy'
        return false
      }
    } catch (err: any) {
      console.error('Error updating pharmacy:', err)
      error.value = processErrors(err, 'An error occurred while updating pharmacy.')
      return false
    } finally {
      isUpdating.value = false
    }
  }

  async function togglePharmacyStatus(id: string) {
    isTogglingStatus.value = true
    error.value = null

    try {
      const response = await apiClient.get<ApiResponse>(`/admin/update-pharmacy-status/${id}`)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Pharmacy status updated successfully')
        return true
      } else {
        error.value = response.data.message || 'Failed to update pharmacy status'
        return false
      }
    } catch (err: any) {
      console.error('Error updating pharmacy status:', err)
      error.value = processErrors(err, 'An error occurred while updating pharmacy status.')
      return false
    } finally {
      isTogglingStatus.value = false
    }
  }

  async function deletePharmacy(id: string) {
    isDeleting.value = true
    error.value = null

    try {
      const response = await apiClient.delete<ApiResponse>(`/admin/delete-pharmacy/${id}`)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Pharmacy deleted successfully')
        return true
      } else {
        error.value = response.data.message || 'Failed to delete pharmacy'
        return false
      }
    } catch (err: any) {
      console.error('Error deleting pharmacy:', err)
      error.value = processErrors(err, 'An error occurred while deleting pharmacy.')
      return false
    } finally {
      isDeleting.value = false
    }
  }

  return {
    // State
    pharmacies,
    selectedPharmacy,
    pagination,
    isLoadingList,
    isLoadingDetails,
    isAdding,
    isUpdating,
    isTogglingStatus,
    isDeleting,
    error,
    fieldErrors,
    searchQuery,
    statusFilter,
    dateRange,
    sortBy,

    // Actions
    fetchPharmacies,
    getPharmacyById,
    addPharmacy,
    updatePharmacy,
    togglePharmacyStatus,
    deletePharmacy,
  }
})
