<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import AppSidebar from '@/components/AppSidebar.vue'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import { Separator } from '@/components/ui/separator'
import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar'
import ThemeToggle from '@/components/ThemeToggle.vue'

const route = useRoute()

const pageTitle = computed(() => {
  return route.meta.title || 'WhiteLblRX Admin'
})

const parentTitle = computed(() => {
  return route.meta.parent || null
})

const parentRouteName = computed(() => {
  return route.meta.parentRouteName || null
})
</script>

<template>
  <SidebarProvider>
    <AppSidebar />
    <SidebarInset>
      <header
        class="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12"
      >
        <div class="flex items-center justify-between gap-2 px-4 w-full">
          <div class="flex items-center gap-2">
            <SidebarTrigger class="-ml-1" />
            <Separator orientation="vertical" class="mr-2 h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem v-if="parentTitle" class="hidden md:block">
                  <RouterLink v-if="parentRouteName" :to="{ name: parentRouteName as string }">
                    {{ parentTitle }}
                  </RouterLink>
                  <BreadcrumbLink v-else>{{ parentTitle }}</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator v-if="parentTitle" class="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>
                    {{ pageTitle }}
                  </BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
          <ThemeToggle />
        </div>
      </header>
      <div class="flex flex-1 flex-col gap-4 p-4 pt-0">
        <router-view />
      </div>
    </SidebarInset>
  </SidebarProvider>
</template>
